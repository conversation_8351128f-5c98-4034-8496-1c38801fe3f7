import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import ConvexHull
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载和预处理数据"""
    df = pd.read_excel('副本数据.xlsx')
    
    # 数据清理
    df_clean = df.dropna(subset=['telecombusiness_stock', 'gpu_stock'])
    df_clean = df_clean[(df_clean['telecombusiness_stock'] > 0) & (df_clean['gpu_stock'] > 0)]
    
    # 删除西藏自治区和青海省的样本
    df_clean = df_clean[~df_clean['province'].isin(['西藏自治区', '青海省'])]
    
    print(f"数据概况:")
    print(f"- 总观测数: {len(df_clean)}")
    print(f"- 年份范围: {df_clean['year'].min()}-{df_clean['year'].max()}")
    print(f"- 省份数量: {df_clean['province'].nunique()}")
    print(f"- 电信业务总量范围: {df_clean['telecombusiness_stock'].min():.0f} - {df_clean['telecombusiness_stock'].max():.0f}")
    print(f"- GPU存量范围: {df_clean['gpu_stock'].min():.0f} - {df_clean['gpu_stock'].max():.0f}")
    
    return df_clean

def calculate_dea_frontier(inputs, outputs):
    """
    计算DEA效率前沿面
    使用凸包方法确定生产前沿
    """
    # 添加原点以确保规模报酬不变
    points = np.column_stack([inputs, outputs])
    points_with_origin = np.vstack([[0, 0], points])
    
    try:
        # 计算凸包
        hull = ConvexHull(points_with_origin)
        hull_points = points_with_origin[hull.vertices]
        
        # 找到上边界（效率前沿）
        # 按x坐标排序
        sorted_indices = np.argsort(hull_points[:, 0])
        hull_sorted = hull_points[sorted_indices]
        
        # 保留单调递增的上边界
        frontier_points = [hull_sorted[0]]  # 从原点开始
        
        for i in range(1, len(hull_sorted)):
            # 只保留y值不下降的点
            if hull_sorted[i, 1] >= frontier_points[-1][1]:
                frontier_points.append(hull_sorted[i])
        
        frontier_points = np.array(frontier_points)
        return frontier_points[:, 0], frontier_points[:, 1]
        
    except Exception as e:
        print(f"凸包计算失败: {e}")
        # 备选方法：使用包络线
        return calculate_envelope_frontier(inputs, outputs)

def calculate_envelope_frontier(inputs, outputs, n_points=100):
    """
    备选方法：计算包络前沿面
    """
    input_max = np.max(inputs)
    input_range = np.linspace(0, input_max * 1.1, n_points)
    
    frontier_outputs = []
    for inp in input_range:
        if inp == 0:
            frontier_outputs.append(0)
        else:
            # 找到投入不超过inp的所有点中产出最大的
            valid_mask = inputs <= inp
            if np.any(valid_mask):
                max_output = np.max(outputs[valid_mask])
                frontier_outputs.append(max_output)
            else:
                frontier_outputs.append(0)
    
    # 确保单调性
    frontier_outputs = np.array(frontier_outputs)
    for i in range(1, len(frontier_outputs)):
        if frontier_outputs[i] < frontier_outputs[i-1]:
            frontier_outputs[i] = frontier_outputs[i-1]
    
    return input_range, frontier_outputs

def identify_efficient_units(inputs, outputs, frontier_x, frontier_y, tolerance=0.05):
    """
    识别效率前沿上的决策单元
    """
    efficient_indices = []
    
    for i, (inp, out) in enumerate(zip(inputs, outputs)):
        # 在前沿线上找到对应的理论最大产出
        theoretical_output = np.interp(inp, frontier_x, frontier_y)
        
        # 如果实际产出接近理论最大产出，则认为是效率前沿上的点
        efficiency_ratio = out / theoretical_output if theoretical_output > 0 else 0
        
        if efficiency_ratio >= (1 - tolerance):
            efficient_indices.append(i)
    
    return efficient_indices

def create_dea_frontier_plot():
    """
    创建DEA效率前沿面图
    """
    # 加载数据
    df = load_data()
    
    inputs = df['telecombusiness_stock'].values
    outputs = df['gpu_stock'].values
    
    # 计算效率前沿
    print(f"\n计算DEA效率前沿面...")
    frontier_x, frontier_y = calculate_dea_frontier(inputs, outputs)
    
    # 识别效率前沿上的单元
    efficient_indices = identify_efficient_units(inputs, outputs, frontier_x, frontier_y)
    
    print(f"效率前沿上的观测点数量: {len(efficient_indices)}")
    
    # 计算效率分数
    efficiency_scores = []
    for inp, out in zip(inputs, outputs):
        theoretical_output = np.interp(inp, frontier_x, frontier_y)
        efficiency = out / theoretical_output if theoretical_output > 0 else 0
        efficiency_scores.append(min(efficiency, 1.0))  # 限制在1以内
    
    efficiency_scores = np.array(efficiency_scores)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 绘制所有数据点
    scatter = ax.scatter(inputs, outputs, 
                        c=efficiency_scores, 
                        cmap='RdYlBu_r', 
                        alpha=0.7, 
                        s=80,
                        edgecolors='black',
                        linewidth=0.5)
    
    # 绘制效率前沿线
    ax.plot(frontier_x, frontier_y, 
           color='red', 
           linewidth=4, 
           label='DEA效率前沿面',
           alpha=0.9,
           zorder=10)
    
    # 标记效率前沿上的点
    if len(efficient_indices) > 0:
        ax.scatter(inputs[efficient_indices], outputs[efficient_indices], 
                  color='red', 
                  s=150, 
                  marker='*', 
                  label=f'效率前沿点 (n={len(efficient_indices)})',
                  edgecolors='darkred',
                  linewidth=1,
                  zorder=15)
    
    # 标注一些重要的效率前沿点
    top_efficient = sorted(efficient_indices, 
                          key=lambda i: efficiency_scores[i], 
                          reverse=True)[:6]
    
    for idx in top_efficient:
        province = df.iloc[idx]['province']
        year = df.iloc[idx]['year']
        ax.annotate(f'{province}\n({year})', 
                   (inputs[idx], outputs[idx]),
                   xytext=(15, 15), 
                   textcoords='offset points',
                   fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.4', 
                            facecolor='yellow', 
                            alpha=0.8,
                            edgecolor='orange'),
                   arrowprops=dict(arrowstyle='->', 
                                 connectionstyle='arc3,rad=0.1',
                                 color='orange'))
    
    # 设置图形属性
    ax.set_xlabel('数据规模(电信业务总量)', fontsize=14, fontweight='bold')
    ax.set_ylabel('算力水平(GPU存量)', fontsize=14, fontweight='bold')
    ax.set_title('基于数据包络分析(DEA)的数据-算力前沿面\n' +
                '中国29省份 2000-2024年面板数据',
                fontsize=16, fontweight='bold', pad=20)

    # 设置纵坐标从1开始
    current_ylim = ax.get_ylim()
    ax.set_ylim(bottom=1, top=current_ylim[1])
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
    cbar.set_label('技术效率分数', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    
    # 图例和网格
    ax.legend(fontsize=12, loc='upper left')
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 设置坐标轴格式
    ax.ticklabel_format(style='scientific', axis='both', scilimits=(0,0))
    ax.tick_params(labelsize=10)
    
    # 添加统计信息文本框
    stats_text = f"""统计信息:
• 观测数: {len(df)}
• 平均效率: {np.mean(efficiency_scores):.3f}
• 效率前沿点: {len(efficient_indices)}个
• 最高效率: {np.max(efficiency_scores):.3f}"""
    
    ax.text(0.25, 0.98, stats_text, 
           transform=ax.transAxes, 
           fontsize=10,
           verticalalignment='top',
           bbox=dict(boxstyle='round,pad=0.5', 
                    facecolor='lightblue', 
                    alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('DEA效率前沿面最终版.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('DEA效率前沿面最终版.pdf', bbox_inches='tight', facecolor='white')
    plt.show()
    
    # 保存分析结果
    df_results = df.copy()
    df_results['技术效率分数'] = efficiency_scores
    df_results['是否在效率前沿'] = [i in efficient_indices for i in range(len(df))]
    df_results['理论最大GPU存量'] = [np.interp(inp, frontier_x, frontier_y) 
                              for inp in inputs]
    # 将"效率改进潜力"改名为"算力缺口(对数化)"
    df_results['算力缺口(对数化)'] = df_results['理论最大GPU存量'] - df_results['gpu_stock']
    
    # 添加算力缺口列
    df_results['算力缺口'] = np.exp(df_results['理论最大GPU存量']) - np.exp(df_results['gpu_stock'])
    
    # 更新相对算力缺口列的计算表达式
    # 对于算力缺口为0的情况，取ln前需要避免log(0)错误
    df_results['相对算力缺口'] = df_results.apply(
        lambda row: np.log(max(np.exp(row['理论最大GPU存量']) - np.exp(row['gpu_stock']), 1e-10)) / np.exp(row['gpu_stock']) 
        if np.exp(row['理论最大GPU存量']) > np.exp(row['gpu_stock']) else 0, 
        axis=1
    )
    
    # 按效率分数排序
    df_results_sorted = df_results.sort_values('技术效率分数', ascending=False)
    
    print(f"\n=== DEA效率分析结果 ===")
    print(f"平均技术效率: {np.mean(efficiency_scores):.4f}")
    print(f"效率前沿上的省份数量: {len(efficient_indices)}")
    
    print(f"\n前10名高效率省份:")
    for i, (_, row) in enumerate(df_results_sorted.head(10).iterrows()):
        print(f"{i+1:2d}. {row['province']} ({row['year']}): "
              f"效率={row['技术效率分数']:.4f}")
    
    # 保存到Excel
    df_results_sorted.to_excel('DEA效率前沿面分析结果.xlsx', index=False)
    print(f"\n完整结果已保存到: 'DEA效率前沿面分析结果.xlsx'")
    print(f"图形已保存为: 'DEA效率前沿面最终版.png' 和 'DEA效率前沿面最终版.pdf'")
    
    # 在保存分析结果之后，添加热力图绘制代码
    print(f"\n绘制算力缺口(对数化)热力图...")
    
    # 创建省份代码映射字典
    province_code = {
        '北京市': 11, '天津市': 12, '河北省': 13, '山西省': 14, '内蒙古自治区': 15,
        '辽宁省': 21, '吉林省': 22, '黑龙江省': 23,
        '上海市': 31, '江苏省': 32, '浙江省': 33, '安徽省': 34, '福建省': 35, '江西省': 36, '山东省': 37,
        '河南省': 41, '湖北省': 42, '湖南省': 43, '广东省': 44, '广西壮族自治区': 45, '海南省': 46,
        '重庆市': 50, '四川省': 51, '贵州省': 52, '云南省': 53, '西藏自治区': 54,
        '陕西省': 61, '甘肃省': 62, '青海省': 63, '宁夏回族自治区': 64, '新疆维吾尔自治区': 65
    }
    
    # 添加省份代码列
    df_results['province_code'] = df_results['province'].map(province_code)
    
    # 创建热力图数据
    # 先按省份代码排序，再创建透视表
    sorted_provinces = df_results.sort_values('province_code')['province'].unique()
    
    pivot_df = df_results.pivot_table(
        index='province', 
        columns='year', 
        values='算力缺口(对数化)',
        aggfunc='mean'
    )
    
    # 按省份代码重新排序行
    pivot_df = pivot_df.reindex(sorted_provinces)
    
    # 创建新的图形
    plt.figure(figsize=(14, 10))
    
    # 绘制热力图
    heatmap = plt.imshow(
        pivot_df.values, 
        cmap='YlOrRd', 
        aspect='auto',
        interpolation='nearest'
    )
    
    # 设置坐标轴标签
    plt.xticks(
        range(len(pivot_df.columns)), 
        pivot_df.columns, 
        rotation=45
    )
    plt.yticks(
        range(len(pivot_df.index)), 
        pivot_df.index
    )
    
    # 添加颜色条和标题
    cbar = plt.colorbar(heatmap, shrink=0.8)
    cbar.set_label('算力缺口(对数化)', fontsize=12, fontweight='bold')
    plt.title('各省份各年度算力缺口(对数化)热力图 (按省份代码排序)', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('算力缺口(对数化)热力图_按省份代码排序.png', dpi=300, bbox_inches='tight')
    plt.savefig('算力缺口(对数化)热力图_按省份代码排序.pdf', bbox_inches='tight')
    plt.show()
    
    # 在绘制算力缺口(对数化)热力图之后，添加算力缺口热力图
    print(f"\n绘制算力缺口热力图...")
    
    # 创建算力缺口热力图数据
    # 先按省份代码排序，再创建透视表
    gap_pivot_df = df_results.pivot_table(
        index='province', 
        columns='year', 
        values='算力缺口',
        aggfunc='mean'
    )
    
    # 按省份代码重新排序行
    gap_pivot_df = gap_pivot_df.reindex(sorted_provinces)
    
    # 创建新的图形
    plt.figure(figsize=(14, 10))
    
    # 绘制热力图，使用不同的颜色方案
    gap_heatmap = plt.imshow(
        gap_pivot_df.values, 
        cmap='plasma',  # 使用plasma颜色方案，从紫色到黄色
        aspect='auto',
        interpolation='nearest'
    )
    
    # 设置坐标轴标签
    plt.xticks(
        range(len(gap_pivot_df.columns)), 
        gap_pivot_df.columns, 
        rotation=45
    )
    plt.yticks(
        range(len(gap_pivot_df.index)), 
        gap_pivot_df.index
    )
    
    # 添加颜色条和标题
    cbar = plt.colorbar(gap_heatmap, shrink=0.8)
    cbar.set_label('算力缺口 (指数尺度)', fontsize=12, fontweight='bold')
    plt.title('各省份各年度算力缺口热力图 (按省份代码排序)', fontsize=16, fontweight='bold')
    
    # 添加说明文本
    plt.figtext(0.5, 0.01, 
               '算力缺口 = exp(理论最大GPU存量) - exp(实际GPU存量)', 
               ha='center', fontsize=10, 
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])  # 为底部说明文本留出空间
    plt.savefig('算力缺口热力图_按省份代码排序.png', dpi=300, bbox_inches='tight')
    plt.savefig('算力缺口热力图_按省份代码排序.pdf', bbox_inches='tight')
    plt.show()
    
    # 在算力缺口热力图之后，添加相对算力缺口热力图
    print(f"\n绘制相对算力缺口热力图...")
    
    # 创建相对算力缺口热力图数据
    # 先按省份代码排序，再创建透视表
    rel_gap_pivot_df = df_results.pivot_table(
        index='province', 
        columns='year', 
        values='相对算力缺口',
        aggfunc='mean'
    )
    
    # 按省份代码重新排序行
    rel_gap_pivot_df = rel_gap_pivot_df.reindex(sorted_provinces)
    
    # 创建新的图形
    plt.figure(figsize=(14, 10))
    
    # 绘制热力图，使用不同的颜色方案
    rel_gap_heatmap = plt.imshow(
        rel_gap_pivot_df.values, 
        cmap='viridis',  # 使用viridis颜色方案
        aspect='auto',
        interpolation='nearest'
    )
    
    # 设置坐标轴标签
    plt.xticks(
        range(len(rel_gap_pivot_df.columns)), 
        rel_gap_pivot_df.columns, 
        rotation=45
    )
    plt.yticks(
        range(len(rel_gap_pivot_df.index)), 
        rel_gap_pivot_df.index
    )
    
    # 添加颜色条和标题
    cbar = plt.colorbar(rel_gap_heatmap, shrink=0.8)
    cbar.set_label('对数相对算力缺口', fontsize=12, fontweight='bold')
    plt.title('各省份各年度对数相对算力缺口热力图 (按省份代码排序)', fontsize=16, fontweight='bold')
    
    # 添加说明文本
    plt.figtext(0.5, 0.01, 
               '相对算力缺口 = ln(exp(理论最大GPU存量) - exp(实际GPU存量)) / exp(实际GPU存量)', 
               ha='center', fontsize=10, 
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])  # 为底部说明文本留出空间
    plt.savefig('对数相对算力缺口热力图.png', dpi=300, bbox_inches='tight')
    plt.savefig('对数相对算力缺口热力图.pdf', bbox_inches='tight')
    plt.show()
    
    return df_results_sorted

if __name__ == "__main__":
    results = create_dea_frontier_plot()
